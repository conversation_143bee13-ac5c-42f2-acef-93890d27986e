import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,

  Row,
  Col,
  message,
  Tag,
  Modal,
  Popconfirm,
  Alert,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  fetchRulesList,
  deleteRule,
  batchDeleteRules,
  batchUpdateRulesStatus,
  setPagination,
  resetRuleManagementState,
} from "@/redux/ruleManagementPage/ruleManagementPageSlice";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddRuleComponent from "./AddRuleComponent";
import { usePageTabs } from "@/hooks/usePageTabs";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";

const { RangePicker } = DatePicker;

function RuleManagementPage() {
  const dispatch = useDispatch();

  // 获取全局枚举常量
  const {
    getSelectOptions,
    getSelectOptionsByKey,
    getEnumName,
    loading: enumLoading
  } = useGlobalConstants();

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = [
    { label: "全部语言", value: "全部语言" },
    ...getSelectOptionsByKey('Locale.Language')
  ];

  // 从Redux获取状态
  const {
    rulesList = [],
    loading,
    pagination,
  } = useSelector((state) => state.ruleManagement);

  // 打印当前状态，用于调试
  console.log("当前Redux状态:", { rulesList, loading, pagination });

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [indexFilter, setIndexFilter] = useState("");
  const [languageFilter, setLanguageFilter] = useState("全部语言");
  const [dateRange, setDateRange] = useState([null, null]);

  // 批量修改状态Modal相关状态
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("ENABLED");

  // 使用PageTabs管理标签页状态
  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "规则管理",
    tabTypes: {
      add: {
        label: "添加规则",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑规则 - ${record.内容}`,
      },
    },
    dataList: rulesList,
    onSaveSuccess: () => {
      fetchRules();
    },
  });

  // 初始加载数据
  useEffect(() => {
    console.log("初始加载数据");
    fetchRules();

    // 组件卸载时重置状态
    return () => {
      dispatch(resetRuleManagementState());
    };
  }, [dispatch]);

  // 获取规则列表
  const fetchRules = () => {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      indexNumber: indexFilter || undefined,
      language: languageFilter !== "全部语言" ? languageFilter : undefined,
      createTimeStart: dateRange && dateRange[0] ? Math.floor(dateRange[0].valueOf() / 1000) : undefined,
      createTimeEnd: dateRange && dateRange[1] ? Math.floor(dateRange[1].valueOf() / 1000) : undefined,
    };

    console.log("获取规则列表，参数:", params);
    dispatch(fetchRulesList(params));
  };

  // 获取状态标签颜色
  const getStatusColor = (status) => {
    switch (status) {
      case "启用":
        return "green";
      case "禁用":
        return "blue";
      default:
        return "default";
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "状态",
      dataIndex: "状态",
      key: "状态",
      width: 100,
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: "规则类型",
      dataIndex: "规则类型",
      key: "规则类型",
      width: 100,
      sorter: (a, b) => a.规则类型 - b.规则类型,
    },
    {
      title: "索引编号",
      dataIndex: "索引编号",
      key: "索引编号",
      width: 120,
    },
    {
      title: "语言",
      dataIndex: "语言",
      key: "语言",
      width: 120,
      render: (language) => {
        // 使用全局枚举显示语言名称
        return getEnumName('Locale.Language', language) || language;
      },
    },
    {
      title: "内容",
      dataIndex: "内容",
      key: "内容",
      width: 200,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 160,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除"
            description={`确定要删除规则 "${record.内容}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的规则");
      return;
    }

    try {
      await dispatch(batchDeleteRules(selectedRowKeys)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个规则`);
      setSelectedRowKeys([]);
      fetchRules();
    } catch (error) {
      message.error("批量删除失败");
    }
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的规则");
      return;
    }
    setBatchStatusModalVisible(true);
    setSelectedStatus("ENABLED"); // 重置为默认值
  };

  // 批量修改状态确认
  const handleBatchStatusConfirm = async () => {
    try {
      await dispatch(
        batchUpdateRulesStatus({
          ids: selectedRowKeys,
          status: selectedStatus === "ENABLED" ? 1 : 0, // 转换为后端期望的数字格式
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个规则的状态`);
      setSelectedRowKeys([]);
      fetchRules();
      setBatchStatusModalVisible(false);
    } catch (error) {
      message.error("批量修改状态失败");
    }
  };

  // 批量修改状态取消
  const handleBatchStatusCancel = () => {
    setBatchStatusModalVisible(false);
    setSelectedStatus("ENABLED");
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteRule(record.id)).unwrap();
      message.success(`已删除规则 ID: ${record.id}`);
      fetchRules();
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(setPagination({ current: 1 })); // 重置到第一页
    fetchRules();
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    console.log("分页变化:", pagination);
    dispatch(
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    // 直接使用新的pagination参数获取数据，避免状态更新延迟
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      indexNumber: indexFilter || undefined,
      language: languageFilter !== "全部语言" ? languageFilter : undefined,
      createTimeStart: dateRange && dateRange[0] ? Math.floor(dateRange[0].valueOf() / 1000) : undefined,
      createTimeEnd: dateRange && dateRange[1] ? Math.floor(dateRange[1].valueOf() / 1000) : undefined,
    };

    console.log("分页变化后获取规则列表，参数:", params);
    dispatch(fetchRulesList(params));
  };

  return (
    <>
      <Card style={{ backgroundColor: "#fff" }}>
        {/* 页面标签栏 */}
        <div className="page-tabs-wrapper">
          <PageTabs
            activeKey={activeTab}
            onChange={handleTabChange}
            onEdit={handleTabEdit}
            items={tabPanes}
            type="editable-card"
          />
        </div>

        {/* 条件渲染 */}
        {isListTab ? (
          <>
            {/* 筛选条件 */}
            <Card style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col>
                  <Input
                    placeholder="索引编号"
                    value={indexFilter}
                    onChange={(e) => setIndexFilter(e.target.value)}
                    style={{ width: 120 }}
                    prefix={<SearchOutlined />}
                  />
                </Col>
                <Col>
                  <Select
                    value={languageFilter}
                    onChange={setLanguageFilter}
                    style={{ width: 120 }}
                    options={languageOptions}
                    loading={enumLoading}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Col>
                <Col>
                  <span>创建时间范围</span>
                </Col>
                <Col>
                  <RangePicker
                    onChange={(dates) => setDateRange(dates || [null, null])}
                    style={{ width: 240 }}
                  />
                </Col>
                <Col>
                  <Button
                    type="primary"
                    onClick={handleSearch}
                    icon={<SearchOutlined />}
                  >
                    查询
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* 批量操作按钮 */}
            <Card style={{ marginBottom: 16 }}>
              <Space wrap>
                <Button
                  onClick={handleAdd}
                  style={{
                    backgroundColor: "#ff7a00",
                    borderColor: "#ff7a00",
                    color: "white",
                  }}
                  icon={<PlusOutlined />}
                >
                  添加
                </Button>
                <Popconfirm
                  title="确定批量删除"
                  description={`确定要删除选中的 ${selectedRowKeys.length} 个规则吗？`}
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                  disabled={selectedRowKeys.length === 0}
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    disabled={selectedRowKeys.length === 0}
                  >
                    批量删除
                  </Button>
                </Popconfirm>
                <Button
                  onClick={handleBatchModifyStatus}
                  disabled={selectedRowKeys.length === 0}
                  style={{
                    backgroundColor: "#ff7a00",
                    borderColor: "#ff7a00",
                    color: "white",
                  }}
                >
                  批量修改状态
                </Button>
              </Space>
            </Card>

            {/* 数据表格 */}
            <Card>
              <Table
                columns={columns}
                dataSource={rulesList}
                loading={loading}
                rowSelection={rowSelection}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`,
                  pageSizeOptions: ["10", "20", "50"],
                }}
                onChange={handleTableChange}
                rowKey="id"
                scroll={{ x: 1200 }}
                size="middle"
                bordered
              />
            </Card>
          </>
        ) : (
          <AddRuleComponent
            editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
            onSave={handleSaveSuccess}
            onCancel={handleCancel}
            tabKey={activeTab}
            saveTabFormData={saveTabFormData}
            getTabFormData={getTabFormData}
            clearTabFormData={clearTabFormData}
          />
        )}
      </Card>

      {/* 批量修改状态Modal */}
      <Modal
        title="批量修改状态"
        open={batchStatusModalVisible}
        onOk={handleBatchStatusConfirm}
        onCancel={handleBatchStatusCancel}
        okText="确定修改"
        cancelText="取消"
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message={`已选中 ${selectedRowKeys.length} 个规则`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
              请选择要设置的状态：
            </label>
            <Select
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: "100%" }}
              size="large"
              options={getSelectOptions('Common.Status').map(option => ({
                label: option.name,  // 显示使用 name
                value: option.key    // 传值使用 key
              }))}
              loading={enumLoading}
            />
          </div>

          <Alert
            message={`将选中的 ${selectedRowKeys.length} 个规则状态修改为 "${getEnumName('Common.Status', selectedStatus) || selectedStatus}"`}
            type="warning"
            showIcon
          />
        </div>
      </Modal>
    </>
  );
}

export default RuleManagementPage;
