import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Select,
} from "antd";
import { SaveOutlined, UndoOutlined } from "@ant-design/icons";
import {
  addRule,
  updateRule,
} from "@/redux/ruleManagementPage/ruleManagementPageSlice";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";

const { Title } = Typography;
const { Option } = Select;

function AddRuleComponent({
  editingRecord,
  onSave,
  onCancel,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 获取全局枚举常量
  const {
    getSelectOptions,
    getSelectOptionsByKey,
    loading: enumLoading
  } = useGlobalConstants();

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = getSelectOptionsByKey('Locale.Language');

  // 智能表单初始化
  useEffect(() => {
    if (editingRecord) {
      // 编辑模式：设置编辑记录的数据
      form.setFieldsValue({
        状态: editingRecord.状态 === "启用" ? "ENABLED" : "DISABLED",
        规则类型: editingRecord.规则类型,
        索引编号: editingRecord.索引编号,
        语言: editingRecord.语言,
        内容: editingRecord.内容,
      });
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
          form.setFieldsValue({
            语言: 'zh_CN', // 默认中文
          });
        }
      } else {
        form.resetFields();
        form.setFieldsValue({
          语言: 'zh_CN', // 默认中文
        });
      }
    }
  }, [editingRecord, form, tabKey, getTabFormData]);

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 转换字段名称，适配后端API
      const ruleData = {
        status: values.状态 === "ENABLED" ? "1" : "0",
        rulesType: values.规则类型.toString(),
        indexNumber: values.索引编号,
        language: values.语言,
        content: values.内容,
      };

      if (editingRecord) {
        // 更新规则
        await dispatch(
          updateRule({
            rulesId: editingRecord.id,
            ruleData,
          })
        ).unwrap();
        message.success("更新成功");
      } else {
        // 添加规则
        await dispatch(addRule(ruleData)).unwrap();
        message.success("添加成功");
        form.resetFields();
        // 清除保存的表单数据
        if (tabKey && clearTabFormData) {
          clearTabFormData(tabKey);
        }
      }

      onSave && onSave();
    } catch (error) {
      console.error("操作失败:", error);
      message.error(editingRecord ? "更新失败" : "添加失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>{editingRecord ? "编辑规则" : "添加规则"}</Title>
      <Card>
        <Form
          form={form}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onValuesChange={() => {
            // 实时保存表单数据（仅在添加模式下）
            if (!editingRecord && tabKey && saveTabFormData) {
              const formData = form.getFieldsValue();
              const hasData = Object.values(formData).some(
                (value) => value !== undefined && value !== null && value.toString().trim() !== ""
              );
              if (hasData) {
                saveTabFormData(tabKey, formData);
              }
            }
          }}
        >
          <Form.Item
            name="状态"
            label="状态"
            rules={[{ required: true, message: "请选择状态" }]}
          >
            <Select
              placeholder="请选择状态"
              options={getSelectOptions('Common.Status').map(option => ({
                label: option.name,  // 显示使用 name
                value: option.key    // 传值使用 key
              }))}
              loading={enumLoading}
            />
          </Form.Item>
          <Form.Item
            name="规则类型"
            label="规则类型"
            rules={[{ required: true, message: "请选择规则类型" }]}
          >
            <Select placeholder="请选择规则类型">
              <Option value={0}>类型0</Option>
              <Option value={1}>类型1</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="索引编号"
            label="索引编号"
            rules={[
              { required: true, message: "请输入索引编号" },
              { pattern: /^[0-9.]+$/, message: "索引编号只能包含数字和点" },
            ]}
          >
            <Input placeholder="请输入索引编号" />
          </Form.Item>
          <Form.Item
            name="语言"
            label="语言"
            rules={[{ required: true, message: "请选择语言" }]}
          >
            <Select
              placeholder="请选择语言"
              options={languageOptions}
              loading={enumLoading}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
          <Form.Item
            name="内容"
            label="内容"
            rules={[
              { required: true, message: "请输入内容" },
              { max: 200, message: "内容最多200个字符" },
            ]}
          >
            <Input.TextArea rows={4} placeholder="请输入内容" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                onClick={handleFormSubmit}
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存
              </Button>
              <Button
                onClick={handleCancel}
                icon={<UndoOutlined />}
                disabled={loading}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddRuleComponent;
