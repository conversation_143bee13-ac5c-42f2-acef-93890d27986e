import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  getWhitelistList,
  getWhitelistDetail,
  addWhitelist,
  batchAddWhitelist,
  updateWhitelist,
  batchUpdateWhitelistType,
  batchUpdateWhitelistStatus,
  deleteWhitelist,
  batchDeleteWhitelist,
} from "../../services/whitelistService";
import { message } from "antd";
import dayjs from "dayjs";

// 获取白名单列表
export const fetchWhitelistList = createAsyncThunk(
  "whitelist/fetchList",
  async (params, { rejectWithValue }) => {
    try {
      const response = await getWhitelistList(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 获取单个白名单详情
export const fetchWhitelistDetail = createAsyncThunk(
  "whitelist/fetchDetail",
  async (id, { rejectWithValue }) => {
    try {
      const response = await getWhitelistDetail(id);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 添加白名单
export const createWhitelist = createAsyncThunk(
  "whitelist/create",
  async (data, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await addWhitelist(data);
      message.success("添加成功");
      // 添加成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return response;
    } catch (error) {
      message.error("添加失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量添加白名单
export const batchCreateWhitelist = createAsyncThunk(
  "whitelist/batchCreate",
  async (data, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await batchAddWhitelist(data);
      message.success(`成功添加${data.length}条记录`);
      // 添加成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return response;
    } catch (error) {
      message.error("批量添加失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 更新白名单
export const updateWhitelistItem = createAsyncThunk(
  "whitelist/update",
  async ({ id, data }, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await updateWhitelist(id, data);
      message.success("更新成功");
      // 更新成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return { id, ...response };
    } catch (error) {
      message.error("更新失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量更新白名单类型
export const batchUpdateType = createAsyncThunk(
  "whitelist/batchUpdateType",
  async (data, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await batchUpdateWhitelistType(data);
      message.success(`成功更新${data.ids.length}条记录的类型`);
      // 更新成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return response;
    } catch (error) {
      message.error("批量更新类型失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量更新白名单状态
export const batchUpdateStatus = createAsyncThunk(
  "whitelist/batchUpdateStatus",
  async (data, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await batchUpdateWhitelistStatus(data);
      message.success(`成功更新${data.ids.length}条记录的状态`);
      // 更新成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return response;
    } catch (error) {
      message.error("批量更新状态失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 删除白名单
export const removeWhitelist = createAsyncThunk(
  "whitelist/remove",
  async (id, { rejectWithValue, dispatch, getState }) => {
    try {
      await deleteWhitelist(id);
      message.success("删除成功");
      // 删除成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return id;
    } catch (error) {
      message.error("删除失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量删除白名单
export const batchRemoveWhitelist = createAsyncThunk(
  "whitelist/batchRemove",
  async (ids, { rejectWithValue, dispatch, getState }) => {
    try {
      await batchDeleteWhitelist(ids);
      message.success(`成功删除${ids.length}条记录`);
      // 批量删除成功后重新获取数据
      const { searchParams } = getState().whitelist;
      dispatch(fetchWhitelistList(searchParams));
      return ids;
    } catch (error) {
      message.error("批量删除失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 格式化API返回的数据为前端显示格式
const formatWhitelistItem = (item) => {
  return {
    key: item.id.toString(),
    id: item.id,
    状态: item.status === "1" ? "启用" : "禁用",
    语言: item.language || "无",
    类型:
      item.itemType === "1" ? "IP" : item.itemType === "2" ? "域名" : "文本",
    内容: item.itemValue,
    创建时间: dayjs(item.createTime * 1000).format("YYYY-MM-DD HH:mm:ss"),
  };
};

const initialState = {
  list: {
    data: [],
    total: 0,
    curPage: 1,
    maxPage: 1,
    loading: false,
    error: null,
  },
  detail: {
    data: null,
    loading: false,
    error: null,
  },
  deleteData: {
    loading: false,
    error: null,
  },
  batchDeleteData: {
    loading: false,
    error: null,
  },
  searchParams: {
    itemValue: "",
    language: "",
    itemType: "",
    status: "",
    startDate: null,
    endDate: null,
    page: 1,
    pageSize: 20,
  },
  selectedRowKeys: [],
};

const whitelistPageSlice = createSlice({
  name: "whitelistPage",
  initialState,
  reducers: {
    setSearchParams: (state, action) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    setSelectedRowKeys: (state, action) => {
      state.selectedRowKeys = action.payload;
    },
    clearSelectedRowKeys: (state) => {
      state.selectedRowKeys = [];
    },
  },
  extraReducers: (builder) => {
    // 获取列表
    builder
      .addCase(fetchWhitelistList.pending, (state) => {
        state.list.loading = true;
        state.list.error = null;
      })
      .addCase(fetchWhitelistList.fulfilled, (state, action) => {
        state.list.loading = false;
        if (action.payload && action.payload.data) {
          state.list.data = Array.isArray(action.payload.data.data)
            ? action.payload.data.data.map(formatWhitelistItem)
            : action.payload.data.data
              ? [formatWhitelistItem(action.payload.data.data)]
              : [];
          state.list.total = action.payload.data.total || 0;
          state.list.curPage = action.payload.data.curPage || 1;
          state.list.maxPage = action.payload.data.maxPage || 1;
        }
      })
      .addCase(fetchWhitelistList.rejected, (state, action) => {
        state.list.loading = false;
        state.list.error = action.payload;
      })

      // 获取详情
      .addCase(fetchWhitelistDetail.pending, (state) => {
        state.detail.loading = true;
        state.detail.error = null;
      })
      .addCase(fetchWhitelistDetail.fulfilled, (state, action) => {
        state.detail.loading = false;
        state.detail.data = action.payload.data;
      })
      .addCase(fetchWhitelistDetail.rejected, (state, action) => {
        state.detail.loading = false;
        state.detail.error = action.payload;
      })

      // 删除单个
      .addCase(removeWhitelist.pending, (state) => {
        state.deleteData.loading = true;
        state.deleteData.error = null;
      })
      .addCase(removeWhitelist.fulfilled, (state) => {
        state.deleteData.loading = false;
        // 不需要手动更新列表，因为会重新获取数据
      })
      .addCase(removeWhitelist.rejected, (state, action) => {
        state.deleteData.loading = false;
        state.deleteData.error = action.payload;
      })

      // 批量删除
      .addCase(batchRemoveWhitelist.pending, (state) => {
        state.batchDeleteData.loading = true;
        state.batchDeleteData.error = null;
      })
      .addCase(batchRemoveWhitelist.fulfilled, (state) => {
        state.batchDeleteData.loading = false;
        // 清空选中的行
        state.selectedRowKeys = [];
      })
      .addCase(batchRemoveWhitelist.rejected, (state, action) => {
        state.batchDeleteData.loading = false;
        state.batchDeleteData.error = action.payload;
      })

      // 批量更新类型
      .addCase(batchUpdateType.fulfilled, (state) => {
        // 清空选中的行
        state.selectedRowKeys = [];
      })

      // 批量更新状态
      .addCase(batchUpdateStatus.fulfilled, (state) => {
        // 清空选中的行
        state.selectedRowKeys = [];
      });
  },
});

export const { setSearchParams, setSelectedRowKeys, clearSelectedRowKeys } =
  whitelistPageSlice.actions;

export default whitelistPageSlice.reducer;
