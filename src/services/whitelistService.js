import axios from "../api/axios";

/**
 * 获取白名单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export const getWhitelistList = (params) => {
  return axios.get("/whitelist/list", { params });
};

/**
 * 获取单个白名单详情
 * @param {number} id 白名单ID
 * @returns {Promise} 请求Promise
 */
export const getWhitelistDetail = (id) => {
  return axios.get(`/whitelist/${id}`);
};

/**
 * 添加白名单
 * @param {Object} data 白名单数据
 * @returns {Promise} 请求Promise
 */
export const addWhitelist = (data) => {
  return axios.post("/whitelist", data);
};

/**
 * 批量添加白名单
 * @param {Array} data 白名单数据数组
 * @returns {Promise} 请求Promise
 */
export const batchAddWhitelist = (data) => {
  return axios.post("/whitelist/batch", data);
};

/**
 * 更新白名单
 * @param {number} id 白名单ID
 * @param {Object} data 白名单数据
 * @returns {Promise} 请求Promise
 */
export const updateWhitelist = (id, data) => {
  return axios.put(`/whitelist/${id}`, data);
};

/**
 * 批量更新白名单类型
 * @param {Object} data 包含ids和type的对象
 * @returns {Promise} 请求Promise
 */
export const batchUpdateWhitelistType = (data) => {
  // 根据控制器定义，这些参数应该通过query参数传递
  return axios.put("/whitelist/batch/type", null, {
    params: {
      type: data.type,
      ids: data.ids
    }
  });
};

/**
 * 批量更新白名单状态
 * @param {Object} data 包含ids和status的对象
 * @returns {Promise} 请求Promise
 */
export const batchUpdateWhitelistStatus = (data) => {
  // 根据控制器定义，这些参数应该通过query参数传递
  return axios.put("/whitelist/batch/status", null, {
    params: {
      status: data.status,
      ids: data.ids
    }
  });
};

/**
 * 删除白名单
 * @param {number} id 白名单ID
 * @returns {Promise} 请求Promise
 */
export const deleteWhitelist = (id) => {
  return axios.delete(`/whitelist/${id}`);
};

/**
 * 批量删除白名单
 * @param {Array} ids 白名单ID数组
 * @returns {Promise} 请求Promise
 */
export const batchDeleteWhitelist = (ids) => {
  return axios.delete("/whitelist/batch", { params: { ids } });
};
