我将提供多个页面路由进行批量处理。请严格按照以下三阶段顺序对每个页面进行完整处理，必须完成当前页面的所有阶段后才能开始处理下一个页面：

## 处理流程（每个页面必须按此顺序完成）：

### 阶段1：页面功能完整性修复
- 根据 `augment_rules/check_page_complete/fix_page_uncomplete.md` 规则
- 检查并修复页面的基础功能缺失问题
- 确保CRUD操作、表单验证、数据加载等核心功能完整
- 完成后进行功能验证测试

### 阶段2：Modal组件升级
- 根据 `augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md` 规则
- 将Modal.confirm批量操作升级为标准Modal组件
- 改善用户体验和代码结构
- 确保升级后的Modal功能正常工作

### 阶段3：枚举值检查和优化
- 根据 `augment_rules/check_page_complete/check_enum.md` 和更新后的全局枚举最佳实践规则
- 检查硬编码枚举值并替换为全局枚举配置
- 区分处理Locale.Language特殊枚举和其他标准枚举
- 确保枚举显示和传值逻辑正确

## 执行要求：
1. **严格顺序执行**：必须按1→2→3的顺序完成每个页面
2. **阶段完整性**：每个阶段必须完全完成并验证无误后才能进入下一阶段
3. **页面完整性**：必须完成当前页面的所有三个阶段后才能开始处理下一个页面
4. **进度报告**：每完成一个阶段请报告进度状态
5. **问题记录**：如遇到无法自动处理的问题，请记录到相应的检查记录文件`augment_rules/check_page_complete/batch_check_record.md`中