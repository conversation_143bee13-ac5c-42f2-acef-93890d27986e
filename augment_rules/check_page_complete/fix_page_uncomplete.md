# 页面完整性修复规则

## 已知
网站已经运行在`http://localhost:3000`，无需再启动服务进行测试

## 任务概述
根据给定的页面路由（例如：`system-settings/system-config/system-settings`），按顺序执行以下几个独立任务来修复页面存在的问题。

## 执行步骤

### 1. 定位页面文件
- 根据路由找到对应的React组件文件
- 示例：`system-settings/system-config/system-settings` → `SystemSettings.jsx`

### 2. 修复service文件中的GET请求参数格式
- 检查页面对应的service文件中所有GET请求的参数传递方式（当前步骤只需要检查GET请求，POST/PUT/DELETE等其他类型的请求不需要检查）
- **修复规则**：如果参数是直接传递，需要用`{}`包裹成对象形式
- **格式示例**（每个页面的具体接口路径不同）：
  - **修复前**：`api.get('/接口路径', params)`
  - **修复后**：`api.get('/接口路径', { params })`

### 3. 定位对应的控制器文件
**重要提醒**：一个页面通常会调用多个不同的API接口，这些接口可能分布在不同的控制器文件中。

**查找步骤**：
1. 分析页面中使用的所有API接口路径
2. 根据接口路径在`swagger_spider/my-mock-server/routes`目录中找到对应的控制器文件
3. **必须找到所有相关的控制器文件**，不能遗漏任何一个

**控制器与接口的对应关系**：
- 不同功能模块的接口可能在不同的控制器文件中
- 一个页面可能需要：数据查询接口、枚举值接口、操作接口等
- 每种类型的接口可能对应不同的控制器文件

**示例说明**（具体情况需根据实际接口分析）：
某个页面可能使用以下接口：
- `/system/config/list` → 在 `system_config_controller_controller.js` 中
- `/system/enum/status` → 在 `system_enum_controller_controller.js` 中
- `/system/user/info` → 在 `system_user_controller_controller.js` 中

因此需要检查这3个控制器文件中的接口参数定义。

### 4. 修复API参数不匹配问题
- 比较页面中使用的所有接口（GET/POST/PUT/DELETE等）与控制器中定义的接口
- **检查内容**：
  1. 参数名称是否一致
  2. 参数类型是否一致
  3. 是否有参数缺失
  4. 传递给get请求的参数中，是存在否缺少page和pageSize参数的情况
- **修复方法**：
  1. 使用控制器中定义的参数名称和类型来修正页面中的请求参数
  2. 如果页面中缺少控制器要求的参数，需要补充这些缺失的参数
  3. 确保前端传递的参数与后端接口定义完全匹配
  4. 确保传递给get请求的参数中，必须有page和pageSize参数

#### 分层调用结构（通用模式）：
```
第1层：页面组件层
├── [页面组件].jsx (React组件)
    └── 调用页面中的数据获取方法

第2层：状态管理层
├── [对应的slice].js (Redux Slice)
    └── 定义异步action方法

第3层：网络请求层
├── service文件 (API服务层)
    └── 实际的HTTP请求：api.get/post/put/delete('接口路径', 参数)

第4层：后端接口层
├── [对应的controller].js (控制器)
    └── 定义接口及其参数规范
```

#### 检查流程说明：
**重要**：需要检查目标页面中**所有使用的接口**（GET/POST/PUT/DELETE等），不仅仅是一个接口。

1. **页面组件层** - 找到页面中所有调用API的方法
2. **状态管理层** - 追踪这些方法对应的Redux action
3. **网络请求层** - 找到实际的HTTP请求调用
4. **后端接口层** - 在控制器中找到对应接口的参数定义

#### 参数匹配检查：
对于每个接口，需要检查：
- **参数名称匹配**：前端传递的参数名称 vs 后端期望的参数名称
- **参数类型匹配**：前端传递的参数类型 vs 后端期望的参数类型
- **参数完整性**：检查是否有后端要求但前端缺失的参数
- **GET请求参数校验**：检查后端接口参数中有需要page和pageSize但是请求中没有传递

**修复原则**：
- 参数名称不匹配 → 使用后端控制器中定义的参数名称
- 参数类型不匹配 → 使用后端控制器中定义的参数类型
- 参数缺失 → 补充后端要求但前端缺失的参数
- GET请求参数中缺少page和pageSize → 补充page和pageSize参数

### 5. PUT请求参数传递方式验证

**触发条件**：
- 页面包含put请求

**参数位置验证**：
- 所有的put请求，都需要对比控制器中的API定义，确认参数是通过query参数还是request body传递
- 检查前端service层代码中axios调用的参数配置是否正确

**数组参数处理**：
当put请求API要求通过query参数传递数组时（如ids参数），需要使用重复参数名的形式：`ids=1&ids=2&ids=3`

在axios中，可以通过以下方式实现：
```javascript
// 方式1：使用paramsSerializer（推荐）
axios.put('/api/endpoint', null, {
  params: { ids: [1, 2, 3], status: 'active' },
  paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
})

// 方式2：手动构建query字符串
const queryString = ids.map(id => `ids=${id}`).join('&') + `&status=${status}`;
axios.put(`/api/endpoint?${queryString}`)
```

**修复示例**：
```javascript
// 修复前（可能导致参数传递错误）
const batchUpdate= (ids) => {
  return api.put('/api/batch-update', { data: { ids } });
};

// 修复后（根据swagger定义调整）
const batchUpdate = (ids) => {
  // 如果swagger定义为query参数
  return api.put('/api/batch-update', {
    params: { ids },
    paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
  });

  // 或者如果swagger定义为request body
  return api.put('/api/batch-update', { data: { ids } });
};
```

### 6. 为删除功能添加加载状态展示

#### 触发条件：
- 页面包含删除功能但缺少删除过程中的加载状态
- 特别是缺少覆盖在整个列表上的等待状态展示

#### 修复方式：

##### 方案A：使用Redux状态的覆盖式加载（推荐）
适用于已有完整Redux状态管理的页面：

1. **获取Redux中的删除状态**：
   ```jsx
   const {
     dataList,
     searchParams,
     deleteData,        // 单个删除的状态
     batchDeleteData    // 批量删除的状态
   } = useSelector((state) => state.yourSlice);

   // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
   const isDeleting = deleteData.loading || batchDeleteData.loading;
   ```

2. **修改表格的loading属性**：
   ```jsx
   <Table
     columns={columns}
     dataSource={dataList}
     loading={loading || isDeleting}  // 添加删除操作的加载状态
     rowSelection={rowSelection}
     // ... 其他属性
   />
   ```

3. **优化删除函数（移除本地状态管理）**：
   ```jsx
   // 单个删除 - 直接使用Redux状态
   const handleDelete = async (record) => {
     try {
       await dispatch(deleteAction(record.id)).unwrap();
       dispatch(fetchDataList(searchParams));
     } catch (error) {
       message.error("删除失败");
     }
   };

   // 批量删除 - 直接使用Redux状态
   const handleBatchDelete = async () => {
     if (selectedRowKeys.length === 0) {
       message.warning("请选择要删除的项目");
       return;
     }

     try {
       await dispatch(batchDeleteAction(selectedRowKeys)).unwrap();
       setSelectedRowKeys([]);
       dispatch(fetchDataList(searchParams));
     } catch (error) {
       message.error("批量删除失败");
     }
   };
   ```

4. **更新按钮的loading属性**：
   ```jsx
   // 单个删除按钮
   <Button size="small" danger loading={deleteData.loading}>
     删除
   </Button>

   // 批量删除按钮
   <Button
     danger
     icon={<DeleteOutlined />}
     disabled={selectedRowKeys.length === 0}
     loading={batchDeleteData.loading}
   >
     批量删除
   </Button>
   ```

##### 方案B：本地状态管理（备用方案）
适用于Redux状态不完整的页面：

1. **添加本地状态管理**：
   ```jsx
   const [deletingId, setDeletingId] = useState(null);
   const [batchDeleting, setBatchDeleting] = useState(false);
   ```

2. **重构删除函数为async/await模式**：
   ```jsx
   const handleDelete = async (record) => {
     setDeletingId(record.id);
     try {
       await dispatch(deleteAction(record.id)).unwrap();
       message.success("删除成功");
       fetchData();
     } catch (error) {
       message.error("删除失败");
     } finally {
       setDeletingId(null);
     }
   };
   ```

3. **修改按钮loading属性**：
   ```jsx
   // 单个删除
   <Button loading={deletingId === record.id}>删除</Button>
   // 批量删除
   <Button loading={batchDeleting}>批量删除</Button>
   ```

#### 实施原则：
- **优先使用方案A**：利用Redux状态实现覆盖整个表格的加载效果
- 参考项目中已有的最佳实践页面`SystemSettings.jsx`
- 确保删除操作时整个表格被半透明遮罩覆盖，防止用户进行其他操作
- 保持一致的用户体验和实现方式
- 使用try-catch确保错误处理
- 删除成功后及时刷新数据列表

### 7. 修复日期范围选择器空值访问错误

#### 触发条件：
- 页面使用了Ant Design的`RangePicker`组件
- 在处理日期范围数据时出现`Cannot read properties of null (reading '0')`错误

#### 问题原因：
当用户清空日期选择器时，`RangePicker`组件会将值设置为`null`，而不是`[null, null]`。如果代码直接访问`dateRange[0]`或`dateRange[1]`会导致空值访问错误。

#### 修复方式：
1. **在使用日期范围数据前添加空值检查**：
   ```jsx
   // 修复前（会报错）
   const params = {
     startDate: dateRange[0] ? dateRange[0].unix() : null,
     endDate: dateRange[1] ? dateRange[1].unix() : null,
   };

   // 修复后（安全访问）
   const params = {
     startDate: dateRange && dateRange[0] ? dateRange[0].unix() : null,
     endDate: dateRange && dateRange[1] ? dateRange[1].unix() : null,
   };
   ```

2. **在日期范围变更处理函数中确保数组格式**：
   ```jsx
   // 修复前
   const handleDateRangeChange = (dates) => {
     setDateRange(dates);
   };

   // 修复后
   const handleDateRangeChange = (dates) => {
     setDateRange(dates || [null, null]);
   };
   ```

3. **确保状态初始化为数组格式**：
   ```jsx
   const [dateRange, setDateRange] = useState([null, null]);
   ```

#### 常见使用场景：
- 搜索筛选功能中的日期范围选择
- 数据导出功能中的时间范围限制
- 报表查询中的时间段选择

#### 实施原则：
- 始终在访问数组元素前检查数组是否为null
- 保持数据格式的一致性（始终为数组）
- 提供良好的用户体验（允许清空选择）

### 8. 为添加和编辑操作添加等待状态

#### 触发条件：
- 页面包含添加/编辑表单但保存按钮缺少加载状态展示

#### 修复方式：
1. **添加状态管理**：
   ```jsx
   import React, { useEffect, useState } from 'react';
   const [loading, setLoading] = useState(false);
   ```

2. **修改提交函数**：
   ```jsx
   const handleFormSubmit = async () => {
     try {
       setLoading(true);
       const values = await form.validateFields();
       // 执行保存操作
       await dispatch(saveAction(values)).unwrap();
       message.success("保存成功");
       onSave && onSave();
     } catch (error) {
       message.error("保存失败");
     } finally {
       setLoading(false);
     }
   };
   ```

3. **更新按钮状态**：
   ```jsx
   <Button
     type="primary"
     onClick={handleFormSubmit}
     loading={loading}
   >
     保存
   </Button>
   <Button
     onClick={handleCancel}
     disabled={loading}
   >
     取消
   </Button>
   ```

#### 实施原则：
- 保存按钮显示加载动画，防止重复提交
- 取消按钮在保存期间禁用，避免误操作
- 使用try-catch-finally确保状态正确清理
- 保持所有表单组件的一致性体验

### 9. 修复标签页切换时表单数据清空问题

#### 触发条件：
- 页面使用了 `usePageTabs` Hook 管理多个视图（如列表页和添加/编辑页）
- 在添加/编辑页面填写表单数据后，切换到其他标签页再回来时，表单数据被清空
- 用户需要重新填写已输入的数据，影响用户体验

#### 问题原因：
- 标签页切换时组件重新渲染，表单状态被重置
- `usePageTabs` Hook 没有保存表单数据状态
- 表单组件（如 `AddSystemSettings`、`AddBadge`、`AddTag` 等）在初始化时会重置表单

#### 适用页面类型：
- 使用 `usePageTabs` Hook 的所有页面
- 包含添加/编辑表单的页面
- 表单组件名称通常为：`Add[模块名]`、`[模块名]Form`、`Edit[模块名]` 等

#### 修复方式：

##### 1. 修改 `usePageTabs` Hook 添加表单数据管理
```jsx
// 在 src/hooks/usePageTabs.js 中添加
const [tabFormData, setTabFormData] = useState({});

// 保存标签页表单数据
const saveTabFormData = useCallback((tabKey, formData) => {
  setTabFormData(prev => ({
    ...prev,
    [tabKey]: formData
  }));
}, []);

// 获取标签页表单数据
const getTabFormData = useCallback((tabKey) => {
  return tabFormData[tabKey] || null;
}, [tabFormData]);

// 清除标签页表单数据
const clearTabFormData = useCallback((tabKey) => {
  setTabFormData(prev => {
    const newData = { ...prev };
    delete newData[tabKey];
    return newData;
  });
}, []);

// 在标签关闭时清除表单数据
const handleTabEdit = useCallback((targetKey, action) => {
  if (action === 'remove') {
    // 清除对应的表单数据
    clearTabFormData(targetKey);
    // ... 其他关闭逻辑
  }
}, [clearTabFormData, /* 其他依赖 */]);

// 在返回对象中添加表单数据管理函数
return {
  // ... 其他返回值
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
};
```

##### 2. 修改主页面组件传递表单数据管理函数
```jsx
// 在页面组件中（如 SystemSettings.jsx、BadgeManagement.jsx、TagList.jsx 等）
const {
  activeTab,
  editingRecord,
  // ... 其他状态
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
} = usePageTabs({
  listTabLabel: '数据列表',  // 根据具体页面调整
  tabTypes: {
    add: {
      label: '添加数据',      // 根据具体页面调整
      prefix: 'add'
    },
    edit: {
      label: '编辑',
      prefix: 'edit',
      getLabelFn: (record) => `编辑数据 - ${record.name}` // 根据具体字段调整
    }
  },
  dataList: dataList,       // 传入对应的数据列表
  onSaveSuccess: () => {
    fetchData();            // 调用对应的数据获取函数
  },
});

// 传递给表单组件（组件名称根据实际情况调整）
<YourFormComponent
  editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
  onSave={handleSaveSuccess}
  onCancel={handleCancel}
  tabKey={activeTab}
  saveTabFormData={saveTabFormData}
  getTabFormData={getTabFormData}
  clearTabFormData={clearTabFormData}
/>
```

##### 3. 修改表单组件实现数据持久化
```jsx
// 在表单组件中（如 AddSystemSettings.jsx、AddBadge.jsx、AddTag.jsx 等）
function YourFormComponent({
  editingRecord,
  onSave,
  onCancel,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData
}) {
  const [form] = Form.useForm();

  // 智能表单初始化
  useEffect(() => {
    if (editingRecord) {
      // 编辑模式：设置编辑记录的数据（字段名根据实际数据结构调整）
      form.setFieldsValue({
        // 示例字段，根据实际表单字段调整
        name: editingRecord.name,
        description: editingRecord.description,
        status: editingRecord.status,
        // ... 其他字段
      });
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
        }
      } else {
        form.resetFields();
      }
    }
  }, [editingRecord, form, tabKey, getTabFormData]);

  // 实时保存表单数据
  <Form
    form={form}
    layout="vertical"
    onValuesChange={() => {
      // 实时保存表单数据（仅在添加模式下）
      if (!editingRecord && tabKey && saveTabFormData) {
        const formData = form.getFieldsValue();
        const hasData = Object.values(formData).some(
          (value) => value && value.toString().trim() !== ""
        );
        if (hasData) {
          saveTabFormData(tabKey, formData);
        }
      }
    }}
  >
    {/* 表单项 */}
  </Form>

  // 在提交成功和取消时清除数据
  const handleFormSubmit = async () => {
    try {
      // ... 提交逻辑
      if (!editingRecord && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }
      onSave && onSave();
    } catch (error) {
      // 错误处理
    }
  };

  const handleCancel = () => {
    form.resetFields();
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };
}
```

#### 功能特点：
- **数据持久化**：表单数据在标签页切换时保持不丢失
- **实时保存**：用户输入时自动保存，无需手动操作
- **智能清理**：只在提交成功、取消操作或关闭标签页时清除数据
- **独立管理**：每个标签页的表单数据独立存储，互不影响
- **编辑模式兼容**：不影响编辑模式的正常工作

#### 通用实施步骤：
1. **识别页面类型**：确认页面使用了 `usePageTabs` Hook
2. **定位表单组件**：找到对应的添加/编辑表单组件
3. **修改 Hook 调用**：在主页面组件中获取表单数据管理函数
4. **传递管理函数**：将函数传递给表单组件
5. **修改表单组件**：实现智能初始化和实时保存逻辑

#### 常见表单组件命名模式：
- `Add[模块名].jsx`：如 `AddSystemSettings.jsx`、`AddBadge.jsx`
- `[模块名]Form.jsx`：如 `TagForm.jsx`、`UserForm.jsx`
- `Edit[模块名].jsx`：如 `EditPage.jsx`
- `[模块名]Modal.jsx`：如果使用Modal形式

#### 实施原则：
- **通用性**：适用于所有使用 `usePageTabs` 的页面
- **向后兼容**：不影响现有编辑模式的正常工作
- **数据安全**：只在添加模式下保存表单数据，编辑模式使用原有逻辑
- **内存管理**：使用标签页的唯一key作为数据存储标识，确保在适当时机清理数据
- **用户体验**：避免数据意外丢失，提供流畅的操作体验

#### 检查清单：
- [ ] 页面是否使用了 `usePageTabs` Hook？
- [ ] 是否存在添加/编辑表单组件？
- [ ] 表单组件是否接收了表单数据管理函数参数？
- [ ] 是否实现了智能表单初始化逻辑？
- [ ] 是否添加了实时保存机制（`onValuesChange`）？
- [ ] 是否在提交成功和取消时清理数据？
- [ ] 是否只在添加模式下保存数据？
- [ ] 编辑模式是否正常工作？

## 注意事项
- 各个任务之间相对独立，但需要按顺序执行
- 重点关注参数名称和类型，确保前端请求与后端接口定义完全匹配
- 修复时要保持代码的整体结构和逻辑不变
- 需要追踪完整的调用链：页面组件 → 状态管理 → 网络请求 → 后端接口
- 特别注意日期范围选择器的空值处理，防止运行时错误
- 确保所有用户交互都有适当的加载状态反馈