# 全局枚举使用最佳实践规则

## 任务概述
基于全局枚举配置管理系统，为所有页面中的枚举使用提供标准化替换规则。该规则涵盖语言枚举和其他所有枚举类型的正确使用模式。

## 最佳实践参考页面
**参考页面**: `src/pages/SystemSettings/QuickMessage/QuickReply.jsx`
**参考组件**: `src/pages/SystemSettings/QuickMessage/AddQuickReply.jsx`
**示例组件**: `src/components/GlobalConstantsUsageExample.js`

## 核心规则

### 1. 枚举分类和使用规则

#### 1.1 Locale.Language 枚举特殊规则
**⚠️ 重要：Locale.Language 是唯一的特殊枚举**
- **显示规则**：显示和传值都使用 `key` 字段（如 `zh_CN`, `en_US`）
- **传参规则**：API传参也使用 `key` 字段的值
- **globalConstantsManager 已内置特殊处理**：无需手动处理显示逻辑

#### 1.2 其他所有枚举的标准规则
**⚠️ 重要：除 Locale.Language 外的所有枚举都遵循标准规则**
- **显示规则**：使用 `name` 字段进行显示（如 "已验证", "待处理", "启用"）
- **传参规则**：传递参数时使用 `key` 字段的值（如 "VERIFIED", "PENDING", "ENABLED"）
- **适用范围**：包括但不限于：
  - 状态枚举：`User.Status`, `Orders.Status`, `Store.Status` 等
  - 类型枚举：`Payment.PaymentType`, `Tag.UseType`, `Brand.BrandType` 等
  - 验证枚举：`User.Verified`, `UserKyc.Status` 等
  - 优先级枚举：`TodoList.Priority` 等
  - 所有其他业务枚举

### 2. 标准实现模式

#### 2.1 Hook导入和使用
```javascript
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const YourComponent = () => {
  // 获取全局枚举配置
  const { getSelectOptions, getSelectOptionsByKey, getEnumName } = useGlobalConstants();

  // Locale.Language 枚举（特殊处理）
  const languageOptions = getSelectOptionsByKey('Locale.Language');

  // 其他枚举（标准处理）
  const statusOptions = getSelectOptions('User.Status');  // 使用 id 作为 value
  const statusOptionsByKey = getSelectOptionsByKey('User.Status');  // 使用 key 作为 value

  // 带筛选条件的选项
  const languageOptionsWithAll = [
    { label: "全部语言", value: "全部语言" },
    ...getSelectOptionsByKey('Locale.Language')
  ];

  const statusOptionsWithAll = [
    { label: "全部状态", value: "全部状态" },
    ...getSelectOptions('User.Status')
  ];
};
```

#### 2.2 表单中的枚举选择器

**Locale.Language 枚举（特殊处理）**：
```javascript
<Form.Item
  name="language"
  label="语言"
  rules={[{ required: true, message: "请选择语言" }]}
>
  <Select
    placeholder="请选择语言"
    options={getSelectOptionsByKey('Locale.Language')}  // 自动处理显示逻辑
    showSearch
    filterOption={(input, option) =>
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
    }
  />
</Form.Item>
```

**其他枚举（标准处理）**：
```javascript
<Form.Item
  name="status"
  label="状态"
  rules={[{ required: true, message: "请选择状态" }]}
>
  <Select
    placeholder="请选择状态"
    options={getSelectOptions('User.Status')}  // 使用 id 作为 value，name 作为 label
    showSearch
    filterOption={(input, option) =>
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
    }
  />
</Form.Item>

{/* 如果需要使用 key 作为传值，可以使用 getSelectOptionsByKey */}
<Form.Item
  name="statusKey"
  label="状态（使用Key）"
>
  <Select
    placeholder="请选择状态"
    options={getSelectOptionsByKey('User.Status')}  // 使用 key 作为 value，name 作为 label
  />
</Form.Item>
```

#### 2.3 表格列中的枚举显示

**Locale.Language 枚举（特殊处理）**：
```javascript
{
  title: "语言",
  dataIndex: "language",
  key: "language",
  width: 120,
  render: (language) => {
    // Locale.Language 自动返回 key 字段作为显示
    return getEnumName('Locale.Language', language) || language;
  },
}
```

**其他枚举（标准处理）**：
```javascript
{
  title: "状态",
  dataIndex: "status",
  key: "status",
  width: 100,
  render: (status) => {
    // 其他枚举返回 name 字段作为显示
    const statusName = getEnumName('User.Status', status);
    const color = status === 1 ? 'green' : 'red';
    return <Tag color={color}>{statusName || '未知'}</Tag>;
  },
}
```

#### 2.4 筛选条件中的枚举选择

**Locale.Language 枚举（特殊处理）**：
```javascript
<Select
  value={languageFilter}
  onChange={setLanguageFilter}
  style={{ width: 120 }}
  options={languageOptionsWithAll}  // 包含"全部语言"选项
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>
```

**其他枚举（标准处理）**：
```javascript
<Select
  value={statusFilter}
  onChange={setStatusFilter}
  style={{ width: 120 }}
  options={statusOptionsWithAll}  // 包含"全部状态"选项
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>
```

#### 2.5 默认值设置

**Locale.Language 枚举**：
```javascript
// 表单默认值设置
form.setFieldsValue({
  language: 'zh_CN', // 默认中文，使用 key 值
});

// 状态初始化
const [languageFilter, setLanguageFilter] = useState("全部语言");
```

**其他枚举**：
```javascript
// 表单默认值设置（使用 id）
form.setFieldsValue({
  status: 1, // 默认启用状态，使用 id 值
});

// 表单默认值设置（使用 key）
form.setFieldsValue({
  statusKey: 'ENABLED', // 默认启用状态，使用 key 值
});

// 状态初始化
const [statusFilter, setStatusFilter] = useState("全部状态");
```

## 自动替换规则

### 1. 识别需要替换的模式

#### 1.1 硬编码枚举选项

**Locale.Language 枚举需要替换的模式**：
```javascript
// ❌ 需要替换
<Select>
  <Option value="zh_CN">中文</Option>
  <Option value="en_US">英语</Option>
  <Option value="es_ES">西班牙语</Option>
</Select>

// ❌ 需要替换
const languageOptions = [
  { label: "中文", value: "zh_CN" },
  { label: "英语", value: "en_US" },
  { label: "西班牙语", value: "es_ES" }
];
```

**其他枚举需要替换的模式**：
```javascript
// ❌ 需要替换
<Select>
  <Option value={0}>禁用</Option>
  <Option value={1}>启用</Option>
</Select>

// ❌ 需要替换
const statusOptions = [
  { label: "禁用", value: 0 },
  { label: "启用", value: 1 },
  { label: "待审核", value: 2 }
];

// ❌ 需要替换
const getStatusText = (status) => {
  if (status === 0) return '禁用';
  if (status === 1) return '启用';
  return '未知';
};
```

**替换为**：
```javascript
// ✅ Locale.Language 标准实现
const languageOptions = getSelectOptionsByKey('Locale.Language');

<Select
  options={languageOptions}
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>

// ✅ 其他枚举标准实现
const statusOptions = getSelectOptions('User.Status');  // 使用 id 作为 value
// 或者
const statusOptionsByKey = getSelectOptionsByKey('User.Status');  // 使用 key 作为 value

<Select
  options={statusOptions}
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>
```

#### 1.2 硬编码枚举映射

**Locale.Language 枚举需要替换的模式**：
```javascript
// ❌ 需要替换
const languageMap = {
  'zh_CN': '中文',
  'en_US': '英语',
  'es_ES': '西班牙语'
};

// ❌ 需要替换
const getLanguageName = (code) => {
  switch (code) {
    case 'zh_CN': return '中文';
    case 'en_US': return '英语';
    default: return code;
  }
};
```

**其他枚举需要替换的模式**：
```javascript
// ❌ 需要替换
const statusMap = {
  0: '禁用',
  1: '启用',
  2: '待审核'
};

// ❌ 需要替换
const getStatusName = (status) => {
  switch (status) {
    case 0: return '禁用';
    case 1: return '启用';
    case 2: return '待审核';
    default: return '未知';
  }
};
```

**替换为**：
```javascript
// ✅ Locale.Language 标准实现
const languageName = getEnumName('Locale.Language', code) || code;

// ✅ 其他枚举标准实现
const statusName = getEnumName('User.Status', status) || '未知';
```

### 2. 自动替换执行步骤

#### 2.1 检测枚举相关字段

**Locale.Language 枚举检测**：
搜索以下模式的字段和变量：
- 字段名包含：`language`, `lang`, `locale`
- 变量名包含：`language`, `lang`, `locale`
- 注释或标签包含：`语言`, `Language`

**其他枚举检测**：
搜索以下模式的字段和变量：
- 状态相关：`status`, `state`, `condition`
- 类型相关：`type`, `kind`, `category`
- 验证相关：`verified`, `validation`, `approval`
- 优先级相关：`priority`, `level`, `importance`
- 注释或标签包含对应的中文描述

#### 2.2 分析硬编码内容

**Locale.Language 枚举**：
检查是否包含常见语言代码：
- `zh_CN`, `zh_TW`, `en_US`, `en_GB`
- `es_ES`, `fr_FR`, `de_DE`, `ja_JP`
- `ko_KR`, `vi_VN`, `th_TH`, `ar_SA`

**其他枚举**：
检查是否包含常见枚举值：
- 状态值：`0/1`, `'ENABLED'/'DISABLED'`, `'ACTIVE'/'INACTIVE'`
- 类型值：数字ID或字符串KEY
- 中文显示文本：`'启用'/'禁用'`, `'待审核'/'已通过'`

#### 2.3 匹配度评估
- **100%匹配**：枚举值和显示文本与 `all_constant.md` 中的完全一致
- **90%以上匹配**：枚举值基本一致但可能有格式差异
- **低于90%匹配**：使用非标准枚举值或自定义格式

### 3. 替换实施规则

#### 3.1 100%匹配 - 自动替换
**条件**：硬编码枚举值与 `all_constant.md` 中的完全匹配
**操作**：立即执行代码替换，无需确认

**Locale.Language 枚举替换步骤**：
1. 添加 `useGlobalConstants` Hook导入
2. 获取 `getSelectOptionsByKey` 和 `getEnumName` 方法
3. 替换硬编码选项为 `getSelectOptionsByKey('Locale.Language')`
4. 替换硬编码映射为 `getEnumName('Locale.Language', value)`
5. 保持原有的搜索、筛选等功能

**其他枚举替换步骤**：
1. 添加 `useGlobalConstants` Hook导入
2. 获取 `getSelectOptions`、`getSelectOptionsByKey` 和 `getEnumName` 方法
3. 根据传值需求选择合适的方法：
   - 使用 ID 传值：`getSelectOptions('EnumKey')`
   - 使用 Key 传值：`getSelectOptionsByKey('EnumKey')`
4. 替换硬编码映射为 `getEnumName('EnumKey', value)`
5. 保持原有的搜索、筛选等功能

#### 3.2 非100%匹配 - 记录待处理
**条件**：枚举值格式不标准或有自定义逻辑
**操作**：记录到 `check_enum_record.md` 文件

**记录格式**：
```markdown
## [页面路径] - [枚举类型]
- **字段名**: [字段名称]
- **当前实现**: [硬编码内容]
- **匹配的枚举**: [枚举键名，如 User.Status]
- **匹配度**: [百分比]
- **差异说明**: [具体差异，如格式不一致、包含自定义值等]
- **建议**: [是否建议替换及注意事项]
- **时间**: [记录时间]

---
```

## 常见场景处理

### 1. 带"全部"选项的枚举筛选

**Locale.Language 枚举**：
```javascript
// 标准实现
const languageOptions = [
  { label: "全部语言", value: "全部语言" },
  ...getSelectOptionsByKey('Locale.Language')
];
```

**其他枚举**：
```javascript
// 使用 ID 作为值
const statusOptions = [
  { label: "全部状态", value: "全部状态" },
  ...getSelectOptions('User.Status')
];

// 使用 Key 作为值
const statusOptionsByKey = [
  { label: "全部状态", value: "全部状态" },
  ...getSelectOptionsByKey('User.Status')
];
```

### 2. 枚举表单验证

**Locale.Language 枚举**：
```javascript
<Form.Item
  name="language"
  label="语言"
  rules={[
    { required: true, message: "请选择语言" },
    {
      validator: (_, value) => {
        if (value && !getEnumName('Locale.Language', value)) {
          return Promise.reject(new Error('请选择有效的语言'));
        }
        return Promise.resolve();
      }
    }
  ]}
>
```

**其他枚举**：
```javascript
<Form.Item
  name="status"
  label="状态"
  rules={[
    { required: true, message: "请选择状态" },
    {
      validator: (_, value) => {
        if (value !== undefined && !getEnumName('User.Status', value)) {
          return Promise.reject(new Error('请选择有效的状态'));
        }
        return Promise.resolve();
      }
    }
  ]}
>
```

### 3. 枚举相关的条件渲染

**Locale.Language 枚举**：
```javascript
// 根据语言显示不同内容
const isChineseLanguage = (lang) => {
  return ['zh_CN', 'zh_TW'].includes(lang);
};

// 使用枚举验证
const isValidLanguage = (lang) => {
  return getEnumName('Locale.Language', lang) !== '';
};
```

**其他枚举**：
```javascript
// 根据状态显示不同颜色
const getStatusColor = (status) => {
  const statusName = getEnumName('User.Status', status);
  switch (statusName) {
    case '启用': return 'green';
    case '禁用': return 'red';
    default: return 'default';
  }
};

// 使用枚举验证
const isValidStatus = (status) => {
  return getEnumName('User.Status', status) !== '';
};
```

## 验证检查清单

### 1. 功能验证

**Locale.Language 枚举**：
- [ ] 语言选项正确显示（显示 key 字段）
- [ ] 选择的语言值能正确保存（传递 key 字段）
- [ ] 表格中语言显示正确（显示 key 字段）
- [ ] 筛选功能正常工作
- [ ] 搜索功能正常工作

**其他枚举**：
- [ ] 枚举选项正确显示（显示 name 字段）
- [ ] 选择的枚举值能正确保存（传递 id 或 key 字段）
- [ ] 表格中枚举显示正确（显示 name 字段）
- [ ] 筛选功能正常工作
- [ ] 搜索功能正常工作

### 2. 数据验证

**Locale.Language 枚举**：
- [ ] 传参使用 key 字段值
- [ ] 显示使用 key 字段值（特殊规则）
- [ ] 默认值设置正确
- [ ] 表单回显正确

**其他枚举**：
- [ ] 传参使用正确字段值（id 或 key）
- [ ] 显示使用 name 字段值（标准规则）
- [ ] 默认值设置正确
- [ ] 表单回显正确

### 3. 用户体验验证
- [ ] 加载状态正确显示
- [ ] 错误处理得当
- [ ] 搜索过滤响应及时
- [ ] 无不必要的重复请求

## 注意事项

### 1. 枚举类型区分
- **Locale.Language 是特殊枚举**：显示和传值都使用 key 字段
- **其他枚举遵循标准规则**：显示使用 name 字段，传值使用 id 或 key 字段

### 2. 功能保持
- **保持搜索功能**：替换后确保 showSearch 和 filterOption 正常工作
- **保持筛选逻辑**：如"全部语言"、"全部状态"等特殊选项需要保留
- **保持原有交互**：确保用户操作习惯不受影响

### 3. 数据处理
- **默认值处理**：
  - Locale.Language：通常设置为 'zh_CN'
  - 其他枚举：根据业务需求设置合适的默认值
- **向后兼容**：确保现有数据能正确显示和处理
- **传值一致性**：确保前后端传值格式一致

### 4. 性能考虑
- **缓存机制**：利用 globalConstantsManager 的缓存功能
- **避免重复请求**：合理使用 loading 状态
- **按需加载**：只获取需要的枚举配置

## 实施优先级

### 1. 高优先级
- 表单中的枚举选择器（直接影响数据提交）
- 表格中的枚举显示（影响数据展示）

### 2. 中优先级
- 筛选条件中的枚举选项
- 状态标签和颜色显示

### 3. 低优先级
- 工具提示和帮助文本中的枚举引用
- 日志和调试信息中的枚举显示

## 适用范围

本规则适用于项目中的所有枚举类型，包括但不限于：
- **Locale.Language**（特殊处理）
- **User.Status, User.Verified, User.Gender**
- **Orders.Status, Orders.PaymentType**
- **Store.Status, StoreStaff.Status**
- **Payment.Status, Payment.PaymentType**
- **TodoList.Status, TodoList.Priority**
- **Tag.Status, Tag.UseType**
- **Brand.Status, Brand.BrandType**
- **所有其他业务枚举**

通过遵循这些规则，可以确保所有页面中的枚举使用方式保持一致，提高代码的可维护性和用户体验的统一性。
