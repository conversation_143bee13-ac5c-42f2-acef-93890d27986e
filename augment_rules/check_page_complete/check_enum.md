# 页面枚举修复规则

## 任务概述
已知 `all_constant.md` 中包含系统中所有需要的枚举值，`globalConstantsManager.js` 是管理这些枚举值的工具类。
我需要你根据给定的页面路由（例如：`system-settings/system-config/system-settings`）根据页面的业务逻辑，从 `all_constant.md` 文件中找到适合的枚举值，如果找到请使用 `globalConstantsManager.js` 管理的枚举值来替换页面中相关的内容，如果没有适合的，不需要强行替换，保持原样即可。

## 处理原则**：
- **100%匹配**：立即使用`augment_rules/check_page_complete/enum_best_practices.md`这个规则执行代码替换
- **非100%匹配**：将页面路由和不是100%匹配的枚举记录到`augment_rules/check_page_complete/check_enum_record.md` 文件
- **未找到匹配**：将页面路由和不是100%匹配的枚举记录到`augment_rules/check_page_complete/check_enum_record.md`文件

